{"name": "toolbc.sgd.web", "classpathEntries": [{"kind": "binary", "path": "C:\\Program Files\\Java\\jre1.8.0_261\\lib\\resources.jar", "javadocContainerUrl": "https://docs.oracle.com/javase/1/docs/api/", "isSystem": true, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Program Files\\Java\\jre1.8.0_261\\lib\\rt.jar", "javadocContainerUrl": "https://docs.oracle.com/javase/1/docs/api/", "isSystem": true, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Program Files\\Java\\jre1.8.0_261\\lib\\jsse.jar", "javadocContainerUrl": "https://docs.oracle.com/javase/1/docs/api/", "isSystem": true, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Program Files\\Java\\jre1.8.0_261\\lib\\jce.jar", "javadocContainerUrl": "https://docs.oracle.com/javase/1/docs/api/", "isSystem": true, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Program Files\\Java\\jre1.8.0_261\\lib\\charsets.jar", "javadocContainerUrl": "https://docs.oracle.com/javase/1/docs/api/", "isSystem": true, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Program Files\\Java\\jre1.8.0_261\\lib\\jfr.jar", "javadocContainerUrl": "https://docs.oracle.com/javase/1/docs/api/", "isSystem": true, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\projectlombok\\lombok\\1.16.20\\lombok-1.16.20.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.16.20/lombok-1.16.20-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.16.20/lombok-1.16.20-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\net\\sf\\dozer\\dozer\\5.5.1\\dozer-5.5.1.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/net/sf/dozer/dozer/5.5.1/dozer-5.5.1-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/net/sf/dozer/dozer/5.5.1/dozer-5.5.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\commons-beanutils\\commons-beanutils\\1.9.3\\commons-beanutils-1.9.3.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/commons-beanutils/commons-beanutils/1.9.3/commons-beanutils-1.9.3-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/commons-beanutils/commons-beanutils/1.9.3/commons-beanutils-1.9.3-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\commons\\commons-lang3\\3.2.1\\commons-lang3-3.2.1.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.2.1/commons-lang3-3.2.1-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.2.1/commons-lang3-3.2.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\slf4j\\slf4j-api\\1.7.25\\slf4j-api-1.7.25.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.25/slf4j-api-1.7.25-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.25/slf4j-api-1.7.25-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\slf4j\\jcl-over-slf4j\\1.7.25\\jcl-over-slf4j-1.7.25.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/slf4j/jcl-over-slf4j/1.7.25/jcl-over-slf4j-1.7.25-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/slf4j/jcl-over-slf4j/1.7.25/jcl-over-slf4j-1.7.25-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\thymeleaf\\extras\\thymeleaf-extras-springsecurity4\\2.1.3.RELEASE\\thymeleaf-extras-springsecurity4-2.1.3.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/thymeleaf/extras/thymeleaf-extras-springsecurity4/2.1.3.RELEASE/thymeleaf-extras-springsecurity4-2.1.3.RELEASE-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/thymeleaf/extras/thymeleaf-extras-springsecurity4/2.1.3.RELEASE/thymeleaf-extras-springsecurity4-2.1.3.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\boot\\spring-boot-starter-data-jpa\\1.5.10.RELEASE\\spring-boot-starter-data-jpa-1.5.10.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-jpa/1.5.10.RELEASE/spring-boot-starter-data-jpa-1.5.10.RELEASE-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\boot\\spring-boot-starter\\1.5.10.RELEASE\\spring-boot-starter-1.5.10.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/1.5.10.RELEASE/spring-boot-starter-1.5.10.RELEASE-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\boot\\spring-boot-starter-logging\\1.5.10.RELEASE\\spring-boot-starter-logging-1.5.10.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/1.5.10.RELEASE/spring-boot-starter-logging-1.5.10.RELEASE-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\ch\\qos\\logback\\logback-classic\\1.1.11\\logback-classic-1.1.11.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.1.11/logback-classic-1.1.11-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.1.11/logback-classic-1.1.11-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\ch\\qos\\logback\\logback-core\\1.1.11\\logback-core-1.1.11.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.1.11/logback-core-1.1.11-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.1.11/logback-core-1.1.11-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\slf4j\\jul-to-slf4j\\1.7.25\\jul-to-slf4j-1.7.25.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.25/jul-to-slf4j-1.7.25-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.25/jul-to-slf4j-1.7.25-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\slf4j\\log4j-over-slf4j\\1.7.25\\log4j-over-slf4j-1.7.25.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/slf4j/log4j-over-slf4j/1.7.25/log4j-over-slf4j-1.7.25-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/slf4j/log4j-over-slf4j/1.7.25/log4j-over-slf4j-1.7.25-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\yaml\\snakeyaml\\1.17\\snakeyaml-1.17.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.17/snakeyaml-1.17-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.17/snakeyaml-1.17-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\boot\\spring-boot-starter-aop\\1.5.10.RELEASE\\spring-boot-starter-aop-1.5.10.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/1.5.10.RELEASE/spring-boot-starter-aop-1.5.10.RELEASE-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\aspectj\\aspectjweaver\\1.8.13\\aspectjweaver-1.8.13.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.8.13/aspectjweaver-1.8.13-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.8.13/aspectjweaver-1.8.13-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\boot\\spring-boot-starter-jdbc\\1.5.10.RELEASE\\spring-boot-starter-jdbc-1.5.10.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/1.5.10.RELEASE/spring-boot-starter-jdbc-1.5.10.RELEASE-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\tomcat\\tomcat-jdbc\\8.5.27\\tomcat-jdbc-8.5.27.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-jdbc/8.5.27/tomcat-jdbc-8.5.27-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\tomcat\\tomcat-juli\\8.5.27\\tomcat-juli-8.5.27.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-juli/8.5.27/tomcat-juli-8.5.27-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\spring-jdbc\\4.3.14.RELEASE\\spring-jdbc-4.3.14.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/4.3.14.RELEASE/spring-jdbc-4.3.14.RELEASE-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/4.3.14.RELEASE/spring-jdbc-4.3.14.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\hibernate\\hibernate-core\\5.0.12.Final\\hibernate-core-5.0.12.Final.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/hibernate/hibernate-core/5.0.12.Final/hibernate-core-5.0.12.Final-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\jboss\\logging\\jboss-logging\\3.3.1.Final\\jboss-logging-3.3.1.Final.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.3.1.Final/jboss-logging-3.3.1.Final-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.3.1.Final/jboss-logging-3.3.1.Final-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\hibernate\\javax\\persistence\\hibernate-jpa-2.1-api\\1.0.0.Final\\hibernate-jpa-2.1-api-1.0.0.Final.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/hibernate/javax/persistence/hibernate-jpa-2.1-api/1.0.0.Final/hibernate-jpa-2.1-api-1.0.0.Final-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/hibernate/javax/persistence/hibernate-jpa-2.1-api/1.0.0.Final/hibernate-jpa-2.1-api-1.0.0.Final-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\javassist\\javassist\\3.21.0-GA\\javassist-3.21.0-GA.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/javassist/javassist/3.21.0-GA/javassist-3.21.0-GA-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/javassist/javassist/3.21.0-GA/javassist-3.21.0-GA-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\antlr\\antlr\\2.7.7\\antlr-2.7.7.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\jboss\\jandex\\2.0.0.Final\\jandex-2.0.0.Final.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/jboss/jandex/2.0.0.Final/jandex-2.0.0.Final-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\dom4j\\dom4j\\1.6.1\\dom4j-1.6.1.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/dom4j/dom4j/1.6.1/dom4j-1.6.1-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\hibernate\\common\\hibernate-commons-annotations\\5.0.1.Final\\hibernate-commons-annotations-5.0.1.Final.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/hibernate/common/hibernate-commons-annotations/5.0.1.Final/hibernate-commons-annotations-5.0.1.Final-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\hibernate\\hibernate-entitymanager\\5.0.12.Final\\hibernate-entitymanager-5.0.12.Final.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/hibernate/hibernate-entitymanager/5.0.12.Final/hibernate-entitymanager-5.0.12.Final-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\javax\\transaction\\javax.transaction-api\\1.2\\javax.transaction-api-1.2.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/javax/transaction/javax.transaction-api/1.2/javax.transaction-api-1.2-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/javax/transaction/javax.transaction-api/1.2/javax.transaction-api-1.2-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\data\\spring-data-jpa\\1.11.10.RELEASE\\spring-data-jpa-1.11.10.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-jpa/1.11.10.RELEASE/spring-data-jpa-1.11.10.RELEASE-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-jpa/1.11.10.RELEASE/spring-data-jpa-1.11.10.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\data\\spring-data-commons\\1.13.10.RELEASE\\spring-data-commons-1.13.10.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/1.13.10.RELEASE/spring-data-commons-1.13.10.RELEASE-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/1.13.10.RELEASE/spring-data-commons-1.13.10.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\spring-orm\\4.3.14.RELEASE\\spring-orm-4.3.14.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/spring-orm/4.3.14.RELEASE/spring-orm-4.3.14.RELEASE-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/spring-orm/4.3.14.RELEASE/spring-orm-4.3.14.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\spring-context\\4.3.14.RELEASE\\spring-context-4.3.14.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/spring-context/4.3.14.RELEASE/spring-context-4.3.14.RELEASE-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/spring-context/4.3.14.RELEASE/spring-context-4.3.14.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\spring-tx\\4.3.14.RELEASE\\spring-tx-4.3.14.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/spring-tx/4.3.14.RELEASE/spring-tx-4.3.14.RELEASE-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/spring-tx/4.3.14.RELEASE/spring-tx-4.3.14.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\spring-beans\\4.3.14.RELEASE\\spring-beans-4.3.14.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/spring-beans/4.3.14.RELEASE/spring-beans-4.3.14.RELEASE-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/spring-beans/4.3.14.RELEASE/spring-beans-4.3.14.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\spring-aspects\\4.3.14.RELEASE\\spring-aspects-4.3.14.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/spring-aspects/4.3.14.RELEASE/spring-aspects-4.3.14.RELEASE-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/spring-aspects/4.3.14.RELEASE/spring-aspects-4.3.14.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\boot\\spring-boot-starter-security\\1.5.10.RELEASE\\spring-boot-starter-security-1.5.10.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/1.5.10.RELEASE/spring-boot-starter-security-1.5.10.RELEASE-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\spring-aop\\4.3.14.RELEASE\\spring-aop-4.3.14.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/spring-aop/4.3.14.RELEASE/spring-aop-4.3.14.RELEASE-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/spring-aop/4.3.14.RELEASE/spring-aop-4.3.14.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\security\\spring-security-config\\4.2.4.RELEASE\\spring-security-config-4.2.4.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/4.2.4.RELEASE/spring-security-config-4.2.4.RELEASE-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/4.2.4.RELEASE/spring-security-config-4.2.4.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\security\\spring-security-web\\4.2.4.RELEASE\\spring-security-web-4.2.4.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/4.2.4.RELEASE/spring-security-web-4.2.4.RELEASE-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/4.2.4.RELEASE/spring-security-web-4.2.4.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\spring-expression\\4.3.14.RELEASE\\spring-expression-4.3.14.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/spring-expression/4.3.14.RELEASE/spring-expression-4.3.14.RELEASE-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/spring-expression/4.3.14.RELEASE/spring-expression-4.3.14.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\boot\\spring-boot-starter-thymeleaf\\1.5.10.RELEASE\\spring-boot-starter-thymeleaf-1.5.10.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-thymeleaf/1.5.10.RELEASE/spring-boot-starter-thymeleaf-1.5.10.RELEASE-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\thymeleaf\\thymeleaf-spring4\\2.1.6.RELEASE\\thymeleaf-spring4-2.1.6.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf-spring4/2.1.6.RELEASE/thymeleaf-spring4-2.1.6.RELEASE-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf-spring4/2.1.6.RELEASE/thymeleaf-spring4-2.1.6.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\thymeleaf\\thymeleaf\\2.1.6.RELEASE\\thymeleaf-2.1.6.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf/2.1.6.RELEASE/thymeleaf-2.1.6.RELEASE-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf/2.1.6.RELEASE/thymeleaf-2.1.6.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\ognl\\ognl\\3.0.8\\ognl-3.0.8.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/ognl/ognl/3.0.8/ognl-3.0.8-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/ognl/ognl/3.0.8/ognl-3.0.8-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\unbescape\\unbescape\\1.1.0.RELEASE\\unbescape-1.1.0.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/unbescape/unbescape/1.1.0.RELEASE/unbescape-1.1.0.RELEASE-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/unbescape/unbescape/1.1.0.RELEASE/unbescape-1.1.0.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\nz\\net\\ultraq\\thymeleaf\\thymeleaf-layout-dialect\\1.4.0\\thymeleaf-layout-dialect-1.4.0.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/nz/net/ultraq/thymeleaf/thymeleaf-layout-dialect/1.4.0/thymeleaf-layout-dialect-1.4.0-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/nz/net/ultraq/thymeleaf/thymeleaf-layout-dialect/1.4.0/thymeleaf-layout-dialect-1.4.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\codehaus\\groovy\\groovy\\2.4.13\\groovy-2.4.13.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/codehaus/groovy/groovy/2.4.13/groovy-2.4.13-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/codehaus/groovy/groovy/2.4.13/groovy-2.4.13-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\boot\\spring-boot-starter-web\\1.5.10.RELEASE\\spring-boot-starter-web-1.5.10.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/1.5.10.RELEASE/spring-boot-starter-web-1.5.10.RELEASE-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\boot\\spring-boot-starter-tomcat\\1.5.10.RELEASE\\spring-boot-starter-tomcat-1.5.10.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/1.5.10.RELEASE/spring-boot-starter-tomcat-1.5.10.RELEASE-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\tomcat\\embed\\tomcat-embed-core\\8.5.27\\tomcat-embed-core-8.5.27.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/8.5.27/tomcat-embed-core-8.5.27-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\tomcat\\tomcat-annotations-api\\8.5.27\\tomcat-annotations-api-8.5.27.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-annotations-api/8.5.27/tomcat-annotations-api-8.5.27-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\tomcat\\embed\\tomcat-embed-el\\8.5.27\\tomcat-embed-el-8.5.27.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/8.5.27/tomcat-embed-el-8.5.27-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\tomcat\\embed\\tomcat-embed-websocket\\8.5.27\\tomcat-embed-websocket-8.5.27.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/8.5.27/tomcat-embed-websocket-8.5.27-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\hibernate\\hibernate-validator\\5.3.6.Final\\hibernate-validator-5.3.6.Final.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/hibernate/hibernate-validator/5.3.6.Final/hibernate-validator-5.3.6.Final-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\javax\\validation\\validation-api\\1.1.0.Final\\validation-api-1.1.0.Final.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/javax/validation/validation-api/1.1.0.Final/validation-api-1.1.0.Final-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/javax/validation/validation-api/1.1.0.Final/validation-api-1.1.0.Final-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\fasterxml\\classmate\\1.3.4\\classmate-1.3.4.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.3.4/classmate-1.3.4-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.3.4/classmate-1.3.4-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\fasterxml\\jackson\\core\\jackson-databind\\2.8.10\\jackson-databind-2.8.10.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.8.10/jackson-databind-2.8.10-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.8.10/jackson-databind-2.8.10-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\spring-web\\4.3.14.RELEASE\\spring-web-4.3.14.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/spring-web/4.3.14.RELEASE/spring-web-4.3.14.RELEASE-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/spring-web/4.3.14.RELEASE/spring-web-4.3.14.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\spring-webmvc\\4.3.14.RELEASE\\spring-webmvc-4.3.14.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/4.3.14.RELEASE/spring-webmvc-4.3.14.RELEASE-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/4.3.14.RELEASE/spring-webmvc-4.3.14.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\boot\\spring-boot-devtools\\1.5.10.RELEASE\\spring-boot-devtools-1.5.10.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-devtools/1.5.10.RELEASE/spring-boot-devtools-1.5.10.RELEASE-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-devtools/1.5.10.RELEASE/spring-boot-devtools-1.5.10.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\boot\\spring-boot\\1.5.10.RELEASE\\spring-boot-1.5.10.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/1.5.10.RELEASE/spring-boot-1.5.10.RELEASE-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/1.5.10.RELEASE/spring-boot-1.5.10.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\boot\\spring-boot-autoconfigure\\1.5.10.RELEASE\\spring-boot-autoconfigure-1.5.10.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/1.5.10.RELEASE/spring-boot-autoconfigure-1.5.10.RELEASE-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/1.5.10.RELEASE/spring-boot-autoconfigure-1.5.10.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\mysql\\mysql-connector-java\\5.1.45\\mysql-connector-java-5.1.45.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/5.1.45/mysql-connector-java-5.1.45-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\boot\\spring-boot-starter-test\\1.5.10.RELEASE\\spring-boot-starter-test-1.5.10.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/1.5.10.RELEASE/spring-boot-starter-test-1.5.10.RELEASE-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\boot\\spring-boot-test\\1.5.10.RELEASE\\spring-boot-test-1.5.10.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/1.5.10.RELEASE/spring-boot-test-1.5.10.RELEASE-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/1.5.10.RELEASE/spring-boot-test-1.5.10.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\boot\\spring-boot-test-autoconfigure\\1.5.10.RELEASE\\spring-boot-test-autoconfigure-1.5.10.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/1.5.10.RELEASE/spring-boot-test-autoconfigure-1.5.10.RELEASE-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/1.5.10.RELEASE/spring-boot-test-autoconfigure-1.5.10.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\jayway\\jsonpath\\json-path\\2.2.0\\json-path-2.2.0.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.2.0/json-path-2.2.0-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.2.0/json-path-2.2.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\net\\minidev\\json-smart\\2.2.1\\json-smart-2.2.1.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.2.1/json-smart-2.2.1-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.2.1/json-smart-2.2.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\net\\minidev\\accessors-smart\\1.1\\accessors-smart-1.1.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/1.1/accessors-smart-1.1-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/1.1/accessors-smart-1.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\ow2\\asm\\asm\\5.0.3\\asm-5.0.3.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/ow2/asm/asm/5.0.3/asm-5.0.3-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/ow2/asm/asm/5.0.3/asm-5.0.3-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\junit\\junit\\4.12\\junit-4.12.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/junit/junit/4.12/junit-4.12-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/junit/junit/4.12/junit-4.12-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\assertj\\assertj-core\\2.6.0\\assertj-core-2.6.0.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/assertj/assertj-core/2.6.0/assertj-core-2.6.0-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/assertj/assertj-core/2.6.0/assertj-core-2.6.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\mockito\\mockito-core\\1.10.19\\mockito-core-1.10.19.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/mockito/mockito-core/1.10.19/mockito-core-1.10.19-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/mockito/mockito-core/1.10.19/mockito-core-1.10.19-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\objenesis\\objenesis\\2.1\\objenesis-2.1.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/objenesis/objenesis/2.1/objenesis-2.1-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/objenesis/objenesis/2.1/objenesis-2.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\hamcrest\\hamcrest-core\\1.3\\hamcrest-core-1.3.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\hamcrest\\hamcrest-library\\1.3\\hamcrest-library-1.3.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-library/1.3/hamcrest-library-1.3-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-library/1.3/hamcrest-library-1.3-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\skyscreamer\\jsonassert\\1.4.0\\jsonassert-1.4.0.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.4.0/jsonassert-1.4.0-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.4.0/jsonassert-1.4.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\vaadin\\external\\google\\android-json\\0.0.20131108.vaadin1\\android-json-0.0.20131108.vaadin1.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\spring-core\\4.3.14.RELEASE\\spring-core-4.3.14.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/spring-core/4.3.14.RELEASE/spring-core-4.3.14.RELEASE-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/spring-core/4.3.14.RELEASE/spring-core-4.3.14.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\spring-test\\4.3.14.RELEASE\\spring-test-4.3.14.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/spring-test/4.3.14.RELEASE/spring-test-4.3.14.RELEASE-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/spring-test/4.3.14.RELEASE/spring-test-4.3.14.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\security\\spring-security-test\\4.2.4.RELEASE\\spring-security-test-4.2.4.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-test/4.2.4.RELEASE/spring-security-test-4.2.4.RELEASE-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-test/4.2.4.RELEASE/spring-security-test-4.2.4.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\security\\spring-security-core\\4.2.4.RELEASE\\spring-security-core-4.2.4.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/4.2.4.RELEASE/spring-security-core-4.2.4.RELEASE-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/4.2.4.RELEASE/spring-security-core-4.2.4.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\aopalliance\\aopalliance\\1.0\\aopalliance-1.0.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/aopalliance/aopalliance/1.0/aopalliance-1.0-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\jasperreports-fonts\\jasperreports-fonts\\5.6.0\\jasperreports-fonts-5.6.0.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\net\\sf\\jasperreports\\jasperreports\\6.4.1\\jasperreports-6.4.1.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/net/sf/jasperreports/jasperreports/6.4.1/jasperreports-6.4.1-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/net/sf/jasperreports/jasperreports/6.4.1/jasperreports-6.4.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\commons-collections\\commons-collections\\3.2.2\\commons-collections-3.2.2.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/commons-collections/commons-collections/3.2.2/commons-collections-3.2.2-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/commons-collections/commons-collections/3.2.2/commons-collections-3.2.2-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\commons-digester\\commons-digester\\2.1\\commons-digester-2.1.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/commons-digester/commons-digester/2.1/commons-digester-2.1-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/commons-digester/commons-digester/2.1/commons-digester-2.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\commons-logging\\commons-logging\\1.1.1\\commons-logging-1.1.1.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.1.1/commons-logging-1.1.1-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.1.1/commons-logging-1.1.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\lowagie\\itext\\2.1.7.js6\\itext-2.1.7.js6.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\bouncycastle\\bcprov-jdk15on\\1.52\\bcprov-jdk15on-1.52.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.52/bcprov-jdk15on-1.52-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.52/bcprov-jdk15on-1.52-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\jfree\\jcommon\\1.0.23\\jcommon-1.0.23.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/jfree/jcommon/1.0.23/jcommon-1.0.23-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/jfree/jcommon/1.0.23/jcommon-1.0.23-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\jfree\\jfreechart\\1.0.19\\jfreechart-1.0.19.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/jfree/jfreechart/1.0.19/jfreechart-1.0.19-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/jfree/jfreechart/1.0.19/jfreechart-1.0.19-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\eclipse\\jdt\\core\\compiler\\ecj\\4.3.1\\ecj-4.3.1.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/eclipse/jdt/core/compiler/ecj/4.3.1/ecj-4.3.1-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/eclipse/jdt/core/compiler/ecj/4.3.1/ecj-4.3.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\codehaus\\castor\\castor-xml\\1.3.3\\castor-xml-1.3.3.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/codehaus/castor/castor-xml/1.3.3/castor-xml-1.3.3-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/codehaus/castor/castor-xml/1.3.3/castor-xml-1.3.3-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\codehaus\\castor\\castor-core\\1.3.3\\castor-core-1.3.3.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/codehaus/castor/castor-core/1.3.3/castor-core-1.3.3-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/codehaus/castor/castor-core/1.3.3/castor-core-1.3.3-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\commons-lang\\commons-lang\\2.6\\commons-lang-2.6.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/commons-lang/commons-lang/2.6/commons-lang-2.6-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/commons-lang/commons-lang/2.6/commons-lang-2.6-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\javax\\inject\\javax.inject\\1\\javax.inject-1.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/javax/inject/javax.inject/1/javax.inject-1-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/javax/inject/javax.inject/1/javax.inject-1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\stax\\stax\\1.2.0\\stax-1.2.0.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/stax/stax/1.2.0/stax-1.2.0-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\stax\\stax-api\\1.0.1\\stax-api-1.0.1.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\javax\\xml\\stream\\stax-api\\1.0-2\\stax-api-1.0-2.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/javax/xml/stream/stax-api/1.0-2/stax-api-1.0-2-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\fasterxml\\jackson\\core\\jackson-core\\2.8.10\\jackson-core-2.8.10.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.8.10/jackson-core-2.8.10-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.8.10/jackson-core-2.8.10-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\fasterxml\\jackson\\core\\jackson-annotations\\2.8.0\\jackson-annotations-2.8.0.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.8.0/jackson-annotations-2.8.0-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.8.0/jackson-annotations-2.8.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\lucene\\lucene-core\\4.5.1\\lucene-core-4.5.1.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-core/4.5.1/lucene-core-4.5.1-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-core/4.5.1/lucene-core-4.5.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\lucene\\lucene-analyzers-common\\4.5.1\\lucene-analyzers-common-4.5.1.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-analyzers-common/4.5.1/lucene-analyzers-common-4.5.1-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-analyzers-common/4.5.1/lucene-analyzers-common-4.5.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\lucene\\lucene-queryparser\\4.5.1\\lucene-queryparser-4.5.1.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-queryparser/4.5.1/lucene-queryparser-4.5.1-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-queryparser/4.5.1/lucene-queryparser-4.5.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\lucene\\lucene-queries\\4.5.1\\lucene-queries-4.5.1.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-queries/4.5.1/lucene-queries-4.5.1-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-queries/4.5.1/lucene-queries-4.5.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\lucene\\lucene-sandbox\\4.5.1\\lucene-sandbox-4.5.1.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-sandbox/4.5.1/lucene-sandbox-4.5.1-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-sandbox/4.5.1/lucene-sandbox-4.5.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\jakarta-regexp\\jakarta-regexp\\1.4\\jakarta-regexp-1.4.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\olap4j\\olap4j\\0.9.7.309-JS-3\\olap4j-0.9.7.309-JS-3.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\google\\zxing\\core\\3.2.1\\core-3.2.1.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/google/zxing/core/3.2.1/core-3.2.1-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/google/zxing/core/3.2.1/core-3.2.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\ibm\\icu\\icu4j\\57.1\\icu4j-57.1.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/ibm/icu/icu4j/57.1/icu4j-57.1-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/ibm/icu/icu4j/57.1/icu4j-57.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\commons-io\\commons-io\\2.5\\commons-io-2.5.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/commons-io/commons-io/2.5/commons-io-2.5-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/commons-io/commons-io/2.5/commons-io-2.5-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\poi\\poi-ooxml\\3.17\\poi-ooxml-3.17.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/3.17/poi-ooxml-3.17-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/3.17/poi-ooxml-3.17-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\poi\\poi\\3.17\\poi-3.17.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/apache/poi/poi/3.17/poi-3.17-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/apache/poi/poi/3.17/poi-3.17-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\commons-codec\\commons-codec\\1.10\\commons-codec-1.10.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.10/commons-codec-1.10-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.10/commons-codec-1.10-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\commons\\commons-collections4\\4.1\\commons-collections4-4.1.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.1/commons-collections4-4.1-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.1/commons-collections4-4.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\poi\\poi-ooxml-schemas\\3.17\\poi-ooxml-schemas-3.17.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\xmlbeans\\xmlbeans\\2.6.0\\xmlbeans-2.6.0.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\github\\virtuald\\curvesapi\\1.04\\curvesapi-1.04.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/github/virtuald/curvesapi/1.04/curvesapi-1.04-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/github/virtuald/curvesapi/1.04/curvesapi-1.04-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\commons-fileupload\\commons-fileupload\\1.3.1\\commons-fileupload-1.3.1.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/commons-fileupload/commons-fileupload/1.3.1/commons-fileupload-1.3.1-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/commons-fileupload/commons-fileupload/1.3.1/commons-fileupload-1.3.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\boot\\spring-boot-starter-web-services\\1.5.10.RELEASE\\spring-boot-starter-web-services-1.5.10.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web-services/1.5.10.RELEASE/spring-boot-starter-web-services-1.5.10.RELEASE-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\spring-oxm\\4.3.14.RELEASE\\spring-oxm-4.3.14.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/4.3.14.RELEASE/spring-oxm-4.3.14.RELEASE-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/4.3.14.RELEASE/spring-oxm-4.3.14.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\ws\\spring-ws-core\\2.4.2.RELEASE\\spring-ws-core-2.4.2.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/ws/spring-ws-core/2.4.2.RELEASE/spring-ws-core-2.4.2.RELEASE-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/ws/spring-ws-core/2.4.2.RELEASE/spring-ws-core-2.4.2.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\ws\\spring-xml\\2.4.2.RELEASE\\spring-xml-2.4.2.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/ws/spring-xml/2.4.2.RELEASE/spring-xml-2.4.2.RELEASE-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/ws/spring-xml/2.4.2.RELEASE/spring-xml-2.4.2.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "source", "path": "D:\\Tool SGD\\toolbcsgd\\src\\main\\java", "outputFolder": "D:\\Tool SGD\\toolbcsgd\\target\\classes", "javadocContainerUrl": "file:/D:/Tool%20SGD/toolbcsgd/target/site/apidocs", "isSystem": false, "isOwn": true, "isTest": false, "isJavaContent": true}, {"kind": "source", "path": "D:\\Tool SGD\\toolbcsgd\\src\\test\\java", "outputFolder": "D:\\Tool SGD\\toolbcsgd\\target\\test-classes", "javadocContainerUrl": "file:/D:/Tool%20SGD/toolbcsgd/target/site/apidocs", "isSystem": false, "isOwn": true, "isTest": true, "isJavaContent": true}, {"kind": "source", "path": "D:\\Tool SGD\\toolbcsgd\\src\\main\\resources", "outputFolder": "D:\\Tool SGD\\toolbcsgd\\target\\classes", "isSystem": false, "isOwn": true, "isTest": false, "isJavaContent": false}, {"kind": "source", "path": "D:\\Tool SGD\\toolbcsgd\\src\\test\\resources", "outputFolder": "D:\\Tool SGD\\toolbcsgd\\target\\test-classes", "isSystem": false, "isOwn": true, "isTest": true, "isJavaContent": false}, {"kind": "source", "path": "D:\\Tool SGD\\toolbcsgd\\src\\test\\java", "outputFolder": "D:\\Tool SGD\\toolbcsgd\\target\\test-classes", "javadocContainerUrl": "file:/D:/Tool%20SGD/toolbcsgd/target/site/apidocs", "isSystem": false, "isOwn": true, "isTest": false, "isJavaContent": false}]}
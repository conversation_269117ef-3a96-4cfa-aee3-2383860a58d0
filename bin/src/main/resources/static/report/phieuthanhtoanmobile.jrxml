<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.5.1.final using JasperReports Library version 6.5.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="phieuthanhtoan" pageWidth="226" pageHeight="453" columnWidth="226" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" whenResourceMissingType="Key" isIgnorePagination="true" uuid="9f6093e1-32dd-4bc4-b409-c7c797533826">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="cnqlkhv2_dev"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="background.image.path" value="C:\Users\<USER>\Desktop\hoadon.jpg"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="mm"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="mm"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="mm"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="mm"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<parameter name="tenCongTy" class="java.lang.String"/>
	<parameter name="tenDonVi" class="java.lang.String"/>
	<parameter name="idThang" class="java.lang.Long"/>
	<parameter name="tenThang" class="java.lang.String"/>
	<parameter name="tuNgay" class="java.lang.String"/>
	<parameter name="denNgay" class="java.lang.String"/>
	<parameter name="maKhachHang" class="java.lang.String"/>
	<parameter name="tenKhachHang" class="java.lang.String"/>
	<parameter name="diaChiSuDung" class="java.lang.String"/>
	<parameter name="soDienThoai" class="java.lang.String"/>
	<parameter name="idHoaDon" class="java.lang.Long"/>
	<parameter name="kyHieu" class="java.lang.String"/>
	<parameter name="soHoaDon" class="java.lang.String"/>
	<parameter name="maDongHo" class="java.lang.String"/>
	<parameter name="chiSoCu" class="java.lang.Integer"/>
	<parameter name="chiSoMoi" class="java.lang.Integer"/>
	<parameter name="tieuThu" class="java.lang.Integer"/>
	<parameter name="tienNuoc" class="java.lang.Long"/>
	<parameter name="thue" class="java.lang.Long"/>
	<parameter name="phiBvmt" class="java.lang.Long"/>
	<parameter name="tongTien" class="java.lang.Long"/>
	<parameter name="tienNop" class="java.lang.Long"/>
	<parameter name="ngayIn" class="java.lang.String"/>
	<parameter name="nhanVienThuNgan" class="java.lang.String"/>
	<queryString language="SQL">
		<![CDATA[call layCTHD($P{idHoaDon});]]>
	</queryString>
	<field name="TIEU_THU" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.label" value="TIEU_THU"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="CHI_TIET_HOA_DON"/>
	</field>
	<field name="DON_GIA" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.label" value="DON_GIA"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="CHI_TIET_HOA_DON"/>
	</field>
	<field name="THANH_TIEN" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.label" value="THANH_TIEN"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="CHI_TIET_HOA_DON"/>
	</field>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="320">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<textField>
				<reportElement x="11" y="0" width="204" height="20" uuid="d7f70c51-73a3-4dcb-a8c8-8016ac34bca0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{tenCongTy}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="11" y="20" width="204" height="20" uuid="acfb56e2-4cbd-47e1-9409-2179f6a01000"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["NHÀ MÁY CẤP NƯỚC THÀNH PHỐ"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="11" y="50" width="204" height="20" uuid="26f63071-0752-40ef-98d3-f021313ecaf3"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["GIẤY BIÊN NHẬN"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="11" y="70" width="204" height="20" uuid="a2325002-5393-47ff-b4cb-91f38ed03f2e"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["Hóa đơn tiền nước tháng " + $P{tenThang}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="11" y="90" width="204" height="20" uuid="bac7d593-8f5b-44db-9548-f7bc93c7d968"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["(Từ " + $P{tuNgay} + " đến " + $P{denNgay} + ")"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="11" y="120" width="204" height="20" uuid="6613f2d1-86ad-42d3-b0b7-0d6b785d232f"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Tên KH: " + $P{tenKhachHang}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="11" y="140" width="204" height="20" uuid="48462d5b-9c05-403d-8a81-af991ae85bb5"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["Địa chỉ: " + $P{diaChiSuDung}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="11" y="160" width="204" height="20" uuid="ebb98c35-0387-462b-be01-705b595c04a7"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["Mã KH: " + $P{maKhachHang} + " - SĐT: " + $P{soDienThoai}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="11" y="180" width="204" height="20" uuid="9f1ad33a-85ab-4bad-b5de-288a3397b429"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["Id hóa đơn: " + $P{idHoaDon}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="11" y="200" width="204" height="20" uuid="06a987c1-809a-42ef-8ca8-3469183c5996"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["Seri HĐĐT: " + $P{kyHieu} + " " + String.format("%07d",  Integer.parseInt($P{soHoaDon}))]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="11" y="220" width="204" height="20" uuid="73989ad4-e25d-49d8-85c8-8a8e84e544e2"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["Số đồng hồ: " + ($P{maDongHo} == null ? "" : $P{maDongHo})]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="11" y="240" width="204" height="20" uuid="c1d9a6a5-c874-4420-88a4-0b341cef4713"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["Hình thức thanh toán: Tiền mặt"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="11" y="260" width="204" height="20" uuid="194326b0-692e-4e92-8792-0cb5e9b18561"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["CSM: " + $P{chiSoMoi} + " - CSC: " + $P{chiSoCu} + " - Tiêu thụ: " + $P{tieuThu} + "(m3)"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="11" y="280" width="204" height="20" uuid="8e1e024a-4ac8-461b-b3ff-61f01a6e7cca"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["Chi tiết giá"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="11" y="300" width="53" height="20" uuid="1d88b92b-c086-4f3d-be6f-084ea695bd2e"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["Tiêu thụ"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="64" y="300" width="68" height="20" uuid="b69cbf12-45cc-4d84-9b27-eb59c0c4ca51"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["Đơn giá"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="132" y="300" width="73" height="20" uuid="b94163ad-17f5-4f00-87ef-6c9eb7b80381"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["Thành tiền"]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<detail>
		<band height="20">
			<textField>
				<reportElement x="11" y="0" width="53" height="20" uuid="384f060f-c042-4e1f-8c5b-a834090ef385"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TIEU_THU} + " x"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="64" y="0" width="68" height="20" uuid="7ad2fe3d-ea8c-4026-9521-71a68416d3d0"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.text.DecimalFormat("#,##0.###", new DecimalFormatSymbols(Locale.US)).format($F{DON_GIA}) + " ="]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="132" y="0" width="73" height="20" uuid="fcbe2f31-0649-44d1-be1e-2350fd7cbdf2"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.text.DecimalFormat("#,##0", new DecimalFormatSymbols(Locale.US)).format($F{THANH_TIEN})]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="160">
			<textField>
				<reportElement x="11" y="0" width="204" height="20" uuid="efe14b37-ad52-484c-92cd-ef32d536fe0f"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["Tiền nước: " + new java.text.DecimalFormat("#,##0", new DecimalFormatSymbols(Locale.US)).format($P{tienNuoc}) +"VNĐ"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="11" y="20" width="204" height="20" uuid="5e81a8e4-516d-4e22-a4b9-d2091c434139"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["Thuế GTGT: " + new java.text.DecimalFormat("#,##0", new DecimalFormatSymbols(Locale.US)).format($P{thue}) +"VNĐ"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="11" y="40" width="204" height="20" uuid="54832a73-9cfb-4792-8363-a36f4ddb14db"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["Phí BVMT: " + new java.text.DecimalFormat("#,##0", new DecimalFormatSymbols(Locale.US)).format($P{phiBvmt}) +"VNĐ"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="11" y="60" width="204" height="20" uuid="ee59c153-8843-474b-98fd-b9e7fa26e070"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["Tổng tiền: " + new java.text.DecimalFormat("#,##0", new DecimalFormatSymbols(Locale.US)).format($P{tongTien}) +"VNĐ"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="11" y="80" width="204" height="20" uuid="f3f7140f-e893-435e-a637-8c7b1e97a7f2"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["Tiền nộp: " + new java.text.DecimalFormat("#,##0", new DecimalFormatSymbols(Locale.US)).format($P{tienNop}) +"VNĐ"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="11" y="100" width="204" height="20" uuid="acd567b8-24c7-41ba-9adb-3d5a4cef1f8b"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["Ngày in: " + $P{ngayIn} + " - Online"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="11" y="120" width="204" height="20" uuid="94034c25-b750-4abc-b303-39d438d3f2fe"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["NV thu ngân: " + $P{nhanVienThuNgan}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="11" y="140" width="204" height="20" uuid="57ecd5e5-670b-47b3-af39-0e059ed89913"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Tổng tiền: " + new java.text.DecimalFormat("#,##0", new DecimalFormatSymbols(Locale.US)).format($P{tongTien}) + "VNĐ"]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>

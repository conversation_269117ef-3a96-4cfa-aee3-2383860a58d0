<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.5.1.final using JasperReports Library version 6.5.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="phieuthanhtoan" pageWidth="595" pageHeight="420" orientation="Landscape" columnWidth="380" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" whenResourceMissingType="Key" uuid="9f6093e1-32dd-4bc4-b409-c7c797533826">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="cnqlkhv2_dev"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="background.image.path" value="C:\Users\<USER>\Desktop\hoadon.jpg"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="mm"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="mm"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<parameter name="logo" class="java.lang.String"/>
	<parameter name="idsoghi" class="java.lang.String"/>
	<parameter name="idthang" class="java.lang.Long"/>
	<parameter name="tuNgay" class="java.lang.String"/>
	<parameter name="denNgay" class="java.lang.String"/>
	<parameter name="tenCongTy" class="java.lang.String"/>
	<parameter name="maSoThue" class="java.lang.String"/>
	<parameter name="soDienThoai" class="java.lang.String"/>
	<parameter name="tyLeGTGT" class="java.lang.String"/>
	<queryString language="SQL">
		<![CDATA[call layHoaDonTienNuocTheoSoGhi($P{idthang}, $P{idsoghi});]]>
	</queryString>
	<field name="MA_DONG_HO" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="MA_DONG_HO"/>
	</field>
	<field name="MA_KHACH_HANG" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="MA_KHACH_HANG"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="*"/>
	</field>
	<field name="THU_TU" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.label" value="THU_TU"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="*"/>
	</field>
	<field name="TEN_KHACH_HANG" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="TEN_KHACH_HANG"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="*"/>
	</field>
	<field name="DIA_CHI_THANH_TOAN" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="DIA_CHI_THANH_TOAN"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="*"/>
	</field>
	<field name="MA_SO_THUE" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="MA_SO_THUE"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="*"/>
	</field>
	<field name="CHI_SO_CU" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.label" value="CHI_SO_CU"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="*"/>
	</field>
	<field name="CHI_SO_MOI" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.label" value="CHI_SO_MOI"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="*"/>
	</field>
	<field name="TIEN_NUOC" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.label" value="TIEN_NUOC"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="*"/>
	</field>
	<field name="THUE" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.label" value="THUE"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="*"/>
	</field>
	<field name="PHI_BVMT" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.label" value="PHI_BVMT"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="*"/>
	</field>
	<field name="TONG_TIEN" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.label" value="TONG_TIEN"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="*"/>
	</field>
	<field name="BANG_CHU" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="BANG_CHU"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="*"/>
	</field>
	<field name="THANG_NAM" class="java.lang.Long">
		<property name="com.jaspersoft.studio.field.label" value="THANG_NAM"/>
	</field>
	<field name="TY_LE_PHI_BVMT" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="TY_LE_PHI_BVMT"/>
	</field>
	<field name="TIEU_THU" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="TIEU_THU"/>
	</field>
	<field name="DON_GIA" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="DON_GIA"/>
	</field>
	<field name="THANH_TIEN" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="THANH_TIEN"/>
	</field>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="380">
			<textField>
				<reportElement x="0" y="0" width="555" height="16" uuid="cda7e585-ba23-4ded-934f-9e24969ce131">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{tenCongTy}.toUpperCase()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="48" width="555" height="16" uuid="29e11f51-79be-40cd-8828-e953697df6b2">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["BIÊN NHẬN THANH TOÁN TIỀN NƯỚC"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="330" y="294" width="225" height="16" uuid="c1f78abb-3707-4bc4-a6f6-be533deb6102">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Ngày " + $P{tuNgay}.substring(0, 2) + " tháng " + $F{THANG_NAM}.toString().substring(4, 6) + " năm " + $F{THANG_NAM}.toString().substring(0, 4)]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="330" y="310" width="225" height="20" uuid="db4c5830-ef68-4823-89af-7e2ef3c4f81c">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Nhân viên thu tiền"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="100" y="80" width="455" height="16" uuid="fbb385f4-8d65-4fbe-9f29-c16a0d35e97f">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TEN_KHACH_HANG}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="100" y="96" width="455" height="16" uuid="31c60057-12c3-416c-962d-108f5ed8d546">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DIA_CHI_THANH_TOAN}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="100" y="112" width="455" height="16" uuid="42cd987d-f700-4288-97d1-0a54b3608356">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MA_KHACH_HANG}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="100" y="128" width="455" height="16" uuid="e801a1a2-f750-450d-9851-7e6c174c6643">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MA_DONG_HO}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="274" width="555" height="20" uuid="a52234d0-a20d-43e6-8214-aad7cd50369c">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Số tiền bằng chữ: " + $F{BANG_CHU}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0">
				<reportElement x="460" y="214" width="95" height="20" uuid="b3ba0cf4-ff40-47ee-874b-c6242b0c51b7">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{THUE}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="32" width="100" height="16" uuid="919420ec-a841-4f64-8300-7b93438efa0c">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Điện thoại:"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="16" width="100" height="16" uuid="14d4c667-10ad-47e8-851b-ffa1524b368b">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Mã số thuế:"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="64" width="555" height="16" uuid="514c9603-cf1c-4d7e-87c5-0c1b3ecaf73c">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Kỳ từ ngày " + $P{tuNgay} + " đến ngày " + $P{denNgay}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0">
				<reportElement x="460" y="194" width="95" height="20" uuid="5195106a-3bfe-4231-82a9-5d4b1db958fb">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TIEN_NUOC}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="100" y="16" width="455" height="16" uuid="0ed479ed-c89b-4946-a96e-571aea92acfa">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{maSoThue}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="100" y="32" width="455" height="16" uuid="88544348-4a24-4f1f-bd4f-7485d787c5a0">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{soDienThoai}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="80" width="100" height="16" uuid="41fc566e-8d49-48d5-9e2a-3ed17ae630ec">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Tên KH:"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="96" width="100" height="16" uuid="42a05ae9-f760-42d4-b499-a34e95cfd0ca">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Địa chỉ KH:"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="112" width="100" height="16" uuid="bcff7340-62fa-4e7e-bb85-af85b8afa997">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Mã KH:"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="128" width="100" height="16" uuid="b5bf8986-1a71-4bbf-be41-092f0b1eb296">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Mã đồng hồ:"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="154" width="130" height="20" uuid="0cb22c1b-b71d-4fb6-8325-4b9843464a21">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Chỉ số đồng hồ tháng trước"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="130" y="154" width="120" height="20" uuid="1bb59af4-ce0b-4ca8-a5df-a15b3717c301">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<topPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Chỉ số đồng hồ tháng này"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="250" y="154" width="80" height="20" uuid="1a32b77b-bcc0-46b2-9bcb-3ef16b87ba82">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<topPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Số nước tiêu thụ"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="330" y="154" width="60" height="20" uuid="e336388f-65be-43ec-a9d3-6f995465644c">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<topPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Hệ số nước"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="390" y="154" width="70" height="20" uuid="28ceebbf-92fe-4454-949c-a5c476611c84">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<topPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Đơn giá"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="460" y="154" width="95" height="20" uuid="7223d35d-de25-42ea-8645-4f19e9094d95">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<topPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Thành tiền"]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0">
				<reportElement x="0" y="174" width="130" height="20" uuid="f125500e-8f27-4fce-842c-deb0802ea8da">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CHI_SO_CU}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0">
				<reportElement x="130" y="174" width="120" height="20" uuid="64a4b7e9-4224-4b6e-8ee5-e2e1d7c5caa1">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CHI_SO_MOI}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0">
				<reportElement x="250" y="174" width="80" height="20" uuid="55782181-b6e5-440d-9e92-911f6215e17e">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TIEU_THU}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="330" y="174" width="60" height="20" uuid="2bf41403-4e39-4940-b94f-797c5bdeebd3">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="390" y="174" width="70" height="20" uuid="77b7398e-1ca1-4040-b63d-40bb9826c1e6">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DON_GIA}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="460" y="174" width="95" height="20" uuid="78de2b71-cd14-4877-bb0c-a3061d2dfb07">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{THANH_TIEN}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="194" width="460" height="20" uuid="a6b44f9f-747f-4dda-bcff-412682d224b5">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Cộng tổng:"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="214" width="460" height="20" uuid="bff5d59f-2904-41c9-8700-c0529600d7b0">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Thuế suất GTGT: " + $P{tyLeGTGT} + "%"]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0">
				<reportElement x="460" y="234" width="95" height="20" uuid="c8824472-c1b4-4b8d-83f6-456441d80447">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PHI_BVMT}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="234" width="460" height="20" uuid="05462664-cf25-49bf-9c49-6e8d3272ccb2">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Phí bảo vệ môi trường đối với NTSH:"]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0">
				<reportElement x="460" y="254" width="95" height="20" uuid="ed1141d0-b748-4543-a62f-c4cb2e120535">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TONG_TIEN}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="254" width="460" height="20" uuid="ed064ebd-5156-444d-b3f8-5c4c1c14aa71">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Cộng tổng tiền thanh toán:"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="310" width="240" height="20" uuid="61d18c2c-c7a5-4ba7-aedc-dfef9ee75111">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Khách hàng"]]></textFieldExpression>
			</textField>
			<image>
				<reportElement x="491" y="16" width="64" height="64" uuid="140f2a13-3806-486a-b1f8-8b6b491dba73">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<imageExpression><![CDATA[new java.io.ByteArrayInputStream(org.apache.commons.codec.binary.Base64.decodeBase64($P{logo}.getBytes()))]]></imageExpression>
			</image>
		</band>
	</detail>
</jasperReport>

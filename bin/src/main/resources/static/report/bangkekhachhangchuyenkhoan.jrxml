<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.5.1.final using JasperReports Library version 6.5.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="sudungnuoc_doituong_tong" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" whenResourceMissingType="Key" uuid="9f6093e1-32dd-4bc4-b409-c7c797533826">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="cnqlkhv2_demo"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<parameter name="idthang" class="java.lang.Long"/>
	<parameter name="tennganhang" class="java.lang.String"/>
	<parameter name="nguoiin" class="java.lang.String"/>
	<parameter name="congty" class="java.lang.String"/>
	<parameter name="idnganhang" class="java.lang.Long"/>
	<parameter name="idkhuvuc" class="java.lang.Long"/>
	<queryString language="SQL">
		<![CDATA[call baoCao_BangKeKhachHangChuyenKhoan($P{idthang}, $P{idnganhang}, $P{idkhuvuc});]]>
	</queryString>
	<field name="ID_DOI_TUONG" class="java.lang.Long">
		<property name="com.jaspersoft.studio.field.label" value="ID_DOI_TUONG"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="*"/>
	</field>
	<field name="TEN_DOI_TUONG" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="TEN_DOI_TUONG"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="*"/>
	</field>
	<field name="ID_DUONG" class="java.lang.Long">
		<property name="com.jaspersoft.studio.field.label" value="ID_DUONG"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="*"/>
	</field>
	<field name="TEN_DUONG" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="TEN_DUONG"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="*"/>
	</field>
	<field name="ID_SO_GHI" class="java.lang.Long">
		<property name="com.jaspersoft.studio.field.label" value="ID_SO_GHI"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="*"/>
	</field>
	<field name="TEN_SO_GHI" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="TEN_SO_GHI"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="*"/>
	</field>
	<field name="MA_THU_TU" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.label" value="MA_THU_TU"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="*"/>
	</field>
	<field name="MA_KHACH_HANG" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="MA_KHACH_HANG"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="*"/>
	</field>
	<field name="TEN" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="TEN"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="*"/>
	</field>
	<field name="TEN_KHACH_HANG" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="TEN_KHACH_HANG"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="*"/>
	</field>
	<field name="DIA_CHI_SU_DUNG" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="DIA_CHI_SU_DUNG"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="*"/>
	</field>
	<field name="SO_DIEN_THOAI" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="SO_DIEN_THOAI"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="*"/>
	</field>
	<field name="SO_TAI_KHOAN" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="SO_TAI_KHOAN"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="*"/>
	</field>
	<field name="TEN_TAI_KHOAN" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="TEN_TAI_KHOAN"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="*"/>
	</field>
	<field name="TIEN_THANH_TOAN" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.label" value="TIEN_THANH_TOAN"/>
	</field>
	<field name="THANG_HIEN_THI" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="THANG_HIEN_THI"/>
	</field>
	<variable name="congdoituong" class="java.math.BigDecimal" resetType="Group" resetGroup="GroupDoiTuong" calculation="Sum">
		<variableExpression><![CDATA[$F{TIEN_THANH_TOAN}]]></variableExpression>
	</variable>
	<variable name="tongcong" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{TIEN_THANH_TOAN}]]></variableExpression>
	</variable>
	<variable name="congduong" class="java.math.BigDecimal" resetType="Group" resetGroup="GroupDuong" calculation="Sum">
		<variableExpression><![CDATA[$F{TIEN_THANH_TOAN}]]></variableExpression>
	</variable>
	<variable name="congsoghi" class="java.math.BigDecimal" resetType="Group" resetGroup="GroupSoGhi" calculation="Sum">
		<variableExpression><![CDATA[$F{TIEN_THANH_TOAN}]]></variableExpression>
	</variable>
	<variable name="to_kh" class="java.lang.Integer" resetType="Group" resetGroup="GroupSoGhi" calculation="Count">
		<variableExpression><![CDATA[$F{MA_KHACH_HANG}]]></variableExpression>
	</variable>
	<variable name="phuong_kh" class="java.lang.Integer" resetType="Group" resetGroup="GroupDuong" calculation="Count">
		<variableExpression><![CDATA[$F{MA_KHACH_HANG}]]></variableExpression>
	</variable>
	<variable name="dt_kh" class="java.lang.Integer" resetType="Group" resetGroup="GroupDoiTuong" calculation="Count">
		<variableExpression><![CDATA[$F{MA_KHACH_HANG}]]></variableExpression>
	</variable>
	<variable name="all_kh" class="java.lang.Integer" calculation="Count">
		<variableExpression><![CDATA[$F{MA_KHACH_HANG}]]></variableExpression>
	</variable>
	<group name="GroupDoiTuong">
		<groupExpression><![CDATA[$F{ID_DOI_TUONG}]]></groupExpression>
		<groupHeader>
			<band height="20">
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<textField isStretchWithOverflow="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="0" y="0" width="802" height="20" isPrintWhenDetailOverflows="true" uuid="1a23f899-6709-463d-a3c3-3a63990cd988">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
						<pen lineWidth="1.0" lineStyle="Solid"/>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Đối tượng " + $F{TEN_DOI_TUONG} + ":"]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="20">
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<textField isStretchWithOverflow="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="0" y="0" width="410" height="20" isPrintWhenDetailOverflows="true" uuid="2f9a4adc-c36a-4190-8bf9-6015fc73c104">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
						<pen lineWidth="1.0" lineStyle="Solid"/>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Cộng đối tượng " + $F{TEN_DOI_TUONG} + " (" + $V{dt_kh} + " khách hàng):"]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="410" y="0" width="100" height="20" isPrintWhenDetailOverflows="true" uuid="3b744481-0e04-4adf-a624-4093bd5b2992">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
						<pen lineWidth="1.0" lineStyle="Solid"/>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{congdoituong}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="510" y="0" width="292" height="20" isPrintWhenDetailOverflows="true" uuid="3196fa61-7d5f-4355-a1bb-8bc7d13fe082">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
						<pen lineWidth="1.0" lineStyle="Solid"/>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10"/>
					</textElement>
					<textFieldExpression><![CDATA[""]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="GroupDuong">
		<groupExpression><![CDATA[$F{ID_DUONG}]]></groupExpression>
		<groupHeader>
			<band height="20">
				<textField isStretchWithOverflow="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="0" y="0" width="802" height="20" isPrintWhenDetailOverflows="true" uuid="db14fd75-cd70-4a2b-ac2c-12d2e9178dee">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
						<pen lineWidth="1.0" lineStyle="Solid"/>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["     Phường " + $F{TEN_DUONG} + ":"]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="20">
				<textField isStretchWithOverflow="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="0" y="0" width="410" height="20" isPrintWhenDetailOverflows="true" uuid="df85722a-a876-4c60-bef9-c3da04f63f3d">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
						<pen lineWidth="1.0" lineStyle="Solid"/>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["     Cộng phường " + $F{TEN_DUONG} + " (" + $V{phuong_kh} + " khách hàng):"]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="410" y="0" width="100" height="20" isPrintWhenDetailOverflows="true" uuid="d1f31b86-0acf-476d-bed6-f25898452120">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
						<pen lineWidth="1.0" lineStyle="Solid"/>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{congduong}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="510" y="0" width="292" height="20" isPrintWhenDetailOverflows="true" uuid="e67becc5-9c2d-4f13-840e-f1ecc829554a">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
						<pen lineWidth="1.0" lineStyle="Solid"/>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10"/>
					</textElement>
					<textFieldExpression><![CDATA[""]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="GroupSoGhi">
		<groupExpression><![CDATA[$F{ID_SO_GHI}]]></groupExpression>
		<groupHeader>
			<band height="20">
				<textField isStretchWithOverflow="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="0" y="0" width="802" height="20" isPrintWhenDetailOverflows="true" uuid="c67aa847-2a04-42a9-a2cc-f2b42a9a3843">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
						<pen lineWidth="1.0" lineStyle="Solid"/>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["          " + $F{TEN_SO_GHI} + ":"]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="20">
				<textField isStretchWithOverflow="true" pattern="#,##0">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="510" y="0" width="292" height="20" isPrintWhenDetailOverflows="true" uuid="d57f4734-21d0-4982-ac4a-9a7997dcb001">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
						<pen lineWidth="1.0" lineStyle="Solid"/>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10"/>
					</textElement>
					<textFieldExpression><![CDATA[""]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="410" y="0" width="100" height="20" isPrintWhenDetailOverflows="true" uuid="50066d5a-0e6d-493e-b735-fbb41ec2042d">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
						<pen lineWidth="1.0" lineStyle="Solid"/>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{congsoghi}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="0" y="0" width="410" height="20" isPrintWhenDetailOverflows="true" uuid="3a4804a1-a6c4-488f-9673-16ab816ecd3a">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
						<pen lineWidth="1.0" lineStyle="Solid"/>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["          Cộng " + $F{TEN_SO_GHI} + " (" + $V{to_kh} + " khách hàng):"]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="120" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<textField>
				<reportElement x="0" y="0" width="330" height="20" uuid="31531523-3a00-4de8-b446-0328db57e7b9">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{congty}.toUpperCase()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="510" y="0" width="292" height="20" uuid="9b5dfddc-e208-4188-8bb7-53577c774907">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="588" y="20" width="135" height="20" uuid="7e27c5c1-6bb5-4f2d-92a3-cc9a1746f4e7">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Độc lập - Tự do - Hạnh phúc"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="50" width="802" height="20" uuid="0c610c8d-630b-41b6-8131-f780f7452111">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["BẢNG KÊ KHÁCH HÀNG CHUYỂN KHOẢN THÁNG " + $P{idthang}.toString().substring(4, 6) + " NĂM " + $P{idthang}.toString().substring(0, 4)]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="70" width="802" height="20" uuid="7c58b1ab-fbe9-4b7d-bcde-1c5eb01ad110">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["NGÂN HÀNG: " + $P{tennganhang}.toUpperCase()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="100" width="30" height="20" uuid="8e0cf841-48a2-46c4-a0bc-3d2e1fbb7938">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<pen lineStyle="Solid"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[STT]]></text>
			</staticText>
			<staticText>
				<reportElement x="30" y="100" width="120" height="20" uuid="4b0b9e50-dd21-43eb-ae57-07613a5dd033">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<pen lineStyle="Solid"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Tên khách hàng]]></text>
			</staticText>
			<staticText>
				<reportElement x="150" y="100" width="60" height="20" uuid="7ae2a6f8-0782-406d-9e69-2cbd919f440b">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<pen lineStyle="Solid"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Mã số KH]]></text>
			</staticText>
			<staticText>
				<reportElement x="210" y="100" width="120" height="20" uuid="a1a56cad-6d42-462d-8782-6406cefb28fc">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<pen lineStyle="Solid"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Địa chỉ]]></text>
			</staticText>
			<staticText>
				<reportElement x="330" y="100" width="80" height="20" uuid="9a0252c9-ea70-46f7-bc98-18028ebb4b57">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<pen lineStyle="Solid"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Tháng sử dụng]]></text>
			</staticText>
			<staticText>
				<reportElement x="410" y="100" width="100" height="20" uuid="d93db8a2-dd87-4eeb-8166-c2b33121f3c9">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<pen lineStyle="Solid"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Tổng tiền thanh toán]]></text>
			</staticText>
			<staticText>
				<reportElement x="600" y="100" width="80" height="20" uuid="1b9af357-2c07-4794-979b-62339759c328">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<pen lineStyle="Solid"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Số điện thoại]]></text>
			</staticText>
			<staticText>
				<reportElement x="680" y="100" width="122" height="20" uuid="515b308f-8568-439d-8572-081220eca58a">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<pen lineStyle="Solid"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Tên tài khoản]]></text>
			</staticText>
			<staticText>
				<reportElement x="510" y="100" width="90" height="20" uuid="4cff4365-f050-4311-a41c-659ce18d9efa">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<pen lineStyle="Solid"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Số tài khoản]]></text>
			</staticText>
		</band>
	</title>
	<detail>
		<band height="20" splitType="Prevent">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="0" y="0" width="30" height="20" isPrintWhenDetailOverflows="true" uuid="c9e33e70-ad43-4a5e-be98-191a7e670557">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<pen lineWidth="1.0" lineStyle="Solid"/>
					<topPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{GroupDuong_COUNT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="30" y="0" width="120" height="20" isPrintWhenDetailOverflows="true" uuid="9c765a6f-4b0c-4257-b30c-f4a4a12367b8">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<pen lineWidth="1.0" lineStyle="Solid"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TEN_KHACH_HANG}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="150" y="0" width="60" height="20" isPrintWhenDetailOverflows="true" uuid="699b590f-95d6-490b-af26-74ecd845de0e">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<pen lineWidth="1.0" lineStyle="Solid"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MA_KHACH_HANG}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="210" y="0" width="120" height="20" isPrintWhenDetailOverflows="true" uuid="058bb1c9-ae63-4262-b918-6e10063be746">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<pen lineWidth="1.0" lineStyle="Solid"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DIA_CHI_SU_DUNG}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="330" y="0" width="80" height="20" isPrintWhenDetailOverflows="true" uuid="54fc5385-c872-4f75-ac9a-dc304cfb0ed8">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<pen lineWidth="1.0" lineStyle="Solid"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{THANG_HIEN_THI}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="410" y="0" width="100" height="20" isPrintWhenDetailOverflows="true" uuid="3dbe29ae-ca71-4eb2-a0a7-0e1d5ff63669">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<pen lineWidth="1.0" lineStyle="Solid"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TIEN_THANH_TOAN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="600" y="0" width="80" height="20" isPrintWhenDetailOverflows="true" uuid="2bfbdf97-f4bf-4241-9128-26ff7b114b51">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<pen lineWidth="1.0" lineStyle="Solid"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SO_DIEN_THOAI}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="680" y="0" width="122" height="20" isPrintWhenDetailOverflows="true" uuid="5e592fc4-3548-45ec-b58a-951932976fd0"/>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<pen lineWidth="1.0" lineStyle="Solid"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TEN_TAI_KHOAN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="510" y="0" width="90" height="20" isPrintWhenDetailOverflows="true" uuid="206af52b-200e-4ea1-8e8b-9b3b5b2a176f">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<pen lineWidth="1.0" lineStyle="Solid"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SO_TAI_KHOAN}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<pageFooter>
		<band height="20" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<textField>
				<reportElement x="680" y="0" width="122" height="20" uuid="52df96e0-0a60-4cd7-8a06-d309179e1420">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Trang " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band height="150" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<textField>
				<reportElement x="600" y="40" width="202" height="20" uuid="7c554ea3-f38c-43d7-bc30-478cb00763bb">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Lập bảng"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="600" y="20" width="202" height="20" uuid="563570d3-b8a0-49a0-b16a-21ec111666f7">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Bắc Kạn, ngày      tháng      năm 20   "]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="600" y="130" width="202" height="20" uuid="79e54920-d6e3-410a-886d-b96b1b34ee13">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{nguoiin}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="40" width="210" height="20" uuid="a1825d42-c59f-4dc0-8a19-2ce355714b60">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Phòng Kế toán - Tài vụ"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="330" y="40" width="180" height="20" uuid="3daaf085-6ca6-48e1-939a-a76e0fb11822">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Phòng Công nghệ thông tin"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="510" y="0" width="292" height="20" isPrintWhenDetailOverflows="true" uuid="0386f8ed-9c24-491c-9734-cc544a2b0bba">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<pen lineWidth="1.0" lineStyle="Solid"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[""]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="410" y="0" width="100" height="20" isPrintWhenDetailOverflows="true" uuid="dc1e7710-dd9c-4f2d-8e60-6d4e16da10c4">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<pen lineWidth="1.0" lineStyle="Solid"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{tongcong}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="0" y="0" width="410" height="20" isPrintWhenDetailOverflows="true" uuid="63e829b3-c1c7-4b18-8e13-827b08b4892c">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<pen lineWidth="1.0" lineStyle="Solid"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Tổng cộng (" + $V{all_kh} + " khách hàng):"]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>

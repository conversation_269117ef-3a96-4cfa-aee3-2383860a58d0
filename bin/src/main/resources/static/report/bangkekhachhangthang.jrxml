<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.5.1.final using JasperReports Library version 6.5.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="sudungnuoc_doituong_tong" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" whenResourceMissingType="Key" uuid="9f6093e1-32dd-4bc4-b409-c7c797533826">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="cnqlkhv2_dev"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<parameter name="idthang" class="java.lang.Long"/>
	<parameter name="tenkhuvuc" class="java.lang.String"/>
	<parameter name="nguoiin" class="java.lang.String"/>
	<parameter name="congty" class="java.lang.String"/>
	<parameter name="idkhuvuc" class="java.lang.Long"/>
	<queryString language="SQL">
		<![CDATA[call bangKeKhachHangThang($P{idthang}, $P{idkhuvuc});]]>
	</queryString>
	<field name="ID_DOI_TUONG" class="java.lang.Long">
		<property name="com.jaspersoft.studio.field.label" value="ID_DOI_TUONG"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="DOI_TUONG"/>
	</field>
	<field name="TEN_DOI_TUONG" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="TEN_DOI_TUONG"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="DOI_TUONG"/>
	</field>
	<field name="ID_DUONG" class="java.lang.Long">
		<property name="com.jaspersoft.studio.field.label" value="ID_DUONG"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="DUONG"/>
	</field>
	<field name="TEN_DUONG" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="TEN_DUONG"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="DUONG"/>
	</field>
	<field name="ID_SO_GHI" class="java.lang.Long">
		<property name="com.jaspersoft.studio.field.label" value="ID_SO_GHI"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="SO_GHI"/>
	</field>
	<field name="TEN_SO_GHI" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="TEN_SO_GHI"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="SO_GHI"/>
	</field>
	<field name="MA_THU_TU" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.label" value="MA_THU_TU"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="THONG_TIN_KHACH_HANG"/>
	</field>
	<field name="MA_KHACH_HANG" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="MA_KHACH_HANG"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="THONG_TIN_KHACH_HANG"/>
	</field>
	<field name="TEN_KHACH_HANG" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="TEN_KHACH_HANG"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="THONG_TIN_KHACH_HANG"/>
	</field>
	<field name="DIA_CHI_SU_DUNG" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="DIA_CHI_SU_DUNG"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="THONG_TIN_KHACH_HANG"/>
	</field>
	<field name="SO_DIEN_THOAI" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="SO_DIEN_THOAI"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="THONG_TIN_KHACH_HANG"/>
	</field>
	<field name="CHI_SO_CU" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.label" value="CHI_SO_CU"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="HOA_DON"/>
	</field>
	<field name="CHI_SO_MOI" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.label" value="CHI_SO_MOI"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="HOA_DON"/>
	</field>
	<field name="TIEU_THU" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.label" value="TIEU_THU"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="HOA_DON"/>
	</field>
	<field name="TIEN_NUOC" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.label" value="TIEN_NUOC"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="HOA_DON"/>
	</field>
	<field name="THUE" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.label" value="THUE"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="HOA_DON"/>
	</field>
	<field name="PHI_BVMT" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.label" value="PHI_BVMT"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="HOA_DON"/>
	</field>
	<field name="TONG_TIEN" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.label" value="TONG_TIEN"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="HOA_DON"/>
	</field>
	<variable name="dt_kh" class="java.lang.Integer" resetType="Group" resetGroup="GroupDoiTuong" calculation="Count">
		<variableExpression><![CDATA[$F{MA_KHACH_HANG}]]></variableExpression>
	</variable>
	<variable name="dt_sl" class="java.lang.Integer" resetType="Group" resetGroup="GroupDoiTuong" calculation="Sum">
		<variableExpression><![CDATA[$F{TIEU_THU}]]></variableExpression>
	</variable>
	<variable name="dt_giatri" class="java.math.BigDecimal" resetType="Group" resetGroup="GroupDoiTuong" calculation="Sum">
		<variableExpression><![CDATA[$F{TIEN_NUOC}]]></variableExpression>
	</variable>
	<variable name="dt_vat" class="java.math.BigDecimal" resetType="Group" resetGroup="GroupDoiTuong" calculation="Sum">
		<variableExpression><![CDATA[$F{THUE}]]></variableExpression>
	</variable>
	<variable name="dt_bvmt" class="java.math.BigDecimal" resetType="Group" resetGroup="GroupDoiTuong" calculation="Sum">
		<variableExpression><![CDATA[$F{PHI_BVMT}]]></variableExpression>
	</variable>
	<variable name="dt_tong" class="java.math.BigDecimal" resetType="Group" resetGroup="GroupDoiTuong" calculation="Sum">
		<variableExpression><![CDATA[$F{TONG_TIEN}]]></variableExpression>
	</variable>
	<variable name="all_kh" class="java.lang.Integer" calculation="Count">
		<variableExpression><![CDATA[$F{MA_KHACH_HANG}]]></variableExpression>
	</variable>
	<variable name="all_sl" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{TIEU_THU}]]></variableExpression>
	</variable>
	<variable name="all_giatri" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{TIEN_NUOC}]]></variableExpression>
	</variable>
	<variable name="all_vat" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{THUE}]]></variableExpression>
	</variable>
	<variable name="all_bvmt" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{PHI_BVMT}]]></variableExpression>
	</variable>
	<variable name="all_tong" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{TONG_TIEN}]]></variableExpression>
	</variable>
	<group name="GroupDoiTuong">
		<groupExpression><![CDATA[$F{ID_DOI_TUONG}]]></groupExpression>
		<groupHeader>
			<band height="20">
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<textField isStretchWithOverflow="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="0" y="0" width="555" height="20" isPrintWhenDetailOverflows="true" uuid="caf90c8e-5652-4f79-835f-434c8795e2e0">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
						<pen lineWidth="1.0" lineStyle="Solid"/>
						<topPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10" isBold="true" isItalic="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Đối tượng " + $F{TEN_DOI_TUONG} + ":"]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="20">
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<textField isStretchWithOverflow="true" pattern="#,##0">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="450" y="0" width="105" height="20" isPrintWhenDetailOverflows="true" uuid="b94717b5-27b0-410b-b548-068c58caa780"/>
					<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
						<pen lineWidth="1.0" lineStyle="Solid"/>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10" isBold="true" isItalic="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{dt_tong}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="0" y="0" width="450" height="20" isPrintWhenDetailOverflows="true" uuid="d0b7478f-7b30-4144-95db-4922711cfa8d">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
						<pen lineWidth="1.0" lineStyle="Solid"/>
						<topPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10" isBold="true" isItalic="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Cộng đối tượng " + $F{TEN_DOI_TUONG} + " (" + $V{dt_kh} + " khách hàng):"]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="100" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<textField>
				<reportElement x="0" y="0" width="250" height="20" uuid="31531523-3a00-4de8-b446-0328db57e7b9">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{congty}.toUpperCase()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="250" y="0" width="305" height="20" uuid="9b5dfddc-e208-4188-8bb7-53577c774907">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="335" y="20" width="135" height="20" uuid="7e27c5c1-6bb5-4f2d-92a3-cc9a1746f4e7">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Độc lập - Tự do - Hạnh phúc"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="50" width="555" height="20" uuid="0c610c8d-630b-41b6-8131-f780f7452111">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["BẢNG KÊ KHÁCH HÀNG THÁNG " + $P{idthang}.toString().substring(4, 6) + " NĂM " + $P{idthang}.toString().substring(0, 4)]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="70" width="555" height="20" uuid="7c58b1ab-fbe9-4b7d-bcde-1c5eb01ad110">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["KHU VỰC " + $P{tenkhuvuc}.toUpperCase()]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<columnHeader>
		<band height="20" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<staticText>
				<reportElement x="0" y="0" width="30" height="20" uuid="645b313a-30ed-4a06-80a6-594ffba06fba">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<pen lineStyle="Solid"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[STT]]></text>
			</staticText>
			<staticText>
				<reportElement x="100" y="0" width="120" height="20" uuid="88423f20-edc4-4336-a379-198609d9a1b1">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<pen lineStyle="Solid"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Tên khách hàng]]></text>
			</staticText>
			<staticText>
				<reportElement x="220" y="0" width="140" height="20" uuid="0437cc32-4cfb-495c-905a-67d11e55f83c">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<pen lineStyle="Solid"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Địa chỉ]]></text>
			</staticText>
			<staticText>
				<reportElement x="360" y="0" width="90" height="20" uuid="9f526e7b-0c6d-42b5-8226-eb242e176ceb">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<pen lineStyle="Solid"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Số điện thoại]]></text>
			</staticText>
			<staticText>
				<reportElement x="450" y="0" width="105" height="20" uuid="bb19c6eb-8c79-47d5-9d41-d9f6b3c46561">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<pen lineStyle="Solid"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Tổng tiền thanh toán]]></text>
			</staticText>
			<staticText>
				<reportElement x="30" y="0" width="70" height="20" uuid="ab958335-b3dd-4542-9863-b8dfb11443d5">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<pen lineStyle="Solid"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Đối tượng]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="20" splitType="Prevent">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="0" y="0" width="30" height="20" isPrintWhenDetailOverflows="true" uuid="c9e33e70-ad43-4a5e-be98-191a7e670557">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<pen lineWidth="1.0" lineStyle="Solid"/>
					<topPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{REPORT_COUNT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="100" y="0" width="120" height="20" isPrintWhenDetailOverflows="true" uuid="9c765a6f-4b0c-4257-b30c-f4a4a12367b8">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<pen lineWidth="1.0" lineStyle="Solid"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TEN_KHACH_HANG}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="220" y="0" width="140" height="20" isPrintWhenDetailOverflows="true" uuid="699b590f-95d6-490b-af26-74ecd845de0e">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<pen lineWidth="1.0" lineStyle="Solid"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DIA_CHI_SU_DUNG}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="360" y="0" width="90" height="20" isPrintWhenDetailOverflows="true" uuid="2bfbdf97-f4bf-4241-9128-26ff7b114b51"/>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<pen lineWidth="1.0" lineStyle="Solid"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SO_DIEN_THOAI}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="450" y="0" width="105" height="20" isPrintWhenDetailOverflows="true" uuid="5e592fc4-3548-45ec-b58a-951932976fd0"/>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<pen lineWidth="1.0" lineStyle="Solid"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TONG_TIEN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="30" y="0" width="70" height="20" isPrintWhenDetailOverflows="true" uuid="d54db133-8af0-448b-96da-1334660a2798">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<pen lineWidth="1.0" lineStyle="Solid"/>
					<topPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TEN_DOI_TUONG}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<pageFooter>
		<band height="20" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<textField>
				<reportElement x="415" y="0" width="140" height="20" uuid="52df96e0-0a60-4cd7-8a06-d309179e1420">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Trang " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band height="150" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<textField>
				<reportElement x="370" y="40" width="185" height="20" uuid="7c554ea3-f38c-43d7-bc30-478cb00763bb">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Lập bảng"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="370" y="20" width="185" height="20" uuid="563570d3-b8a0-49a0-b16a-21ec111666f7">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Bắc Kạn, ngày      tháng      năm 20   "]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="370" y="130" width="185" height="20" uuid="79e54920-d6e3-410a-886d-b96b1b34ee13">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{nguoiin}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="40" width="185" height="20" uuid="a1825d42-c59f-4dc0-8a19-2ce355714b60">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Phòng Kế toán - Tài vụ"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="185" y="40" width="185" height="20" uuid="3daaf085-6ca6-48e1-939a-a76e0fb11822">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Phòng Công nghệ thông tin"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="450" y="0" width="105" height="20" isPrintWhenDetailOverflows="true" uuid="7cf564b4-715e-49e8-b68b-b5cabadb3477">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<pen lineWidth="1.0" lineStyle="Solid"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{all_tong}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="0" y="0" width="450" height="20" isPrintWhenDetailOverflows="true" uuid="9900d232-3518-4487-acb4-cbb37d8dccc6">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<pen lineWidth="1.0" lineStyle="Solid"/>
					<topPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Tổng cộng (" + $V{all_kh} + " khách hàng):"]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>

# set default profile
# spring.profiles.active: bkn
# ===============================
# = DATA SOURCE
# ===============================
spring.datasource.url = ******************************************************************************************
spring.datasource.username = bcsgd_dev
spring.datasource.password = Bcsgd_dev@2019

# ===============================
# = JPA / HIBERNATE
# ===============================
spring.jpa.show-sql = true
spring.jpa.properties.hibernate.dialect = org.hibernate.dialect.MySQL5InnoDBDialect
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
spring.jpa.properties.hibernate.proc.param_null_passing = true
spring.http.multipart.enabled = false
server.session.timeout = 10000
# ===== Report path
report.path=e://toolsgd.web/report/
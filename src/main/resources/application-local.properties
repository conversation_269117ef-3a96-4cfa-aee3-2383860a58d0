# set default profile
# spring.profiles.active: bkn
# ===============================
# = DATA SOURCE
# ===============================
spring.datasource.url = ***************************************************************************************
spring.datasource.username = root
spring.datasource.password = 

# ===============================
# = JPA / HIBERNATE
# ===============================
spring.jpa.show-sql = true
spring.jpa.properties.hibernate.dialect = org.hibernate.dialect.MySQL5InnoDBDialect
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
spring.jpa.properties.hibernate.proc.param_null_passing = true
spring.http.multipart.enabled = false
# ===== Report path
report.path=d://toolsgd.web/report/
<!-- Content Header (Page header) -->
<section class="content-header">
     <h1>
		<th:block th:text="${MODEL.title}"></th:block>
		<a data-toggle="modal" data-target="#modIntro" href="#" title="Hướng dẫn sử dụng"><i class="fa fa-question-circle"></i></a>
		<small></small>
     </h1>
     <ol class="breadcrumb">
         <li>
             <a href=".">
                 <i class="fa fa-home"></i>
                 Trang chủ
             </a>
         </li>
         <li class="active" th:text="${MODEL.title}">Actived page</li>
     </ol>
 </section>
 <!-- Main content -->
 <section class="content">
 	<div class="row">
 		<div class="col-md-7">
 			<div class="box box-primary" id="box-ds">
 				<div class="box-header with-border">
 					<h3 class="box-title"><PERSON><PERSON><PERSON></h3>
 				</div>
 				<div class="box-body">
 					<div class="row">
 						<div class="col-md-6">
 							<div class="form-group">
 								<select class="form-control" id="cmb-huyen-filter"></select>
 							</div>
 						</div>
 						<div class="col-md-6">
 							<div class="form-group">
 								<div class="input-group">
 									<input type="text" id="txt-keyword" class="form-control" placeholder="Tên giá nước"/>
 									<span class="input-group-btn">
 										<button type="button" class="btn btn-default btn-flat" id="btn-search"><i class="fa fa-search"></i></button>
 										<button type="button" class="btn btn-default btn-flat" id="btn-clear-search"><i class="fa fa-times-circle"></i></button>
 									</span>
 								</div>
 							</div>
 						</div>
 					</div>
 					<div class="jdgrid" id="grid-ds"></div>
 				</div>
 				<div class="box-footer">
 					<div class="jdpage" id="page-ds"></div>
 				</div>
 			</div>
 		</div>
 		<div class="col-md-5">
 			<div class="box box-success" id="box-frm">
 				<div class="box-header with-border">
 					<h3 class="box-title">Thông tin giá nước</h3>
 				</div>
 				<div class="box-body">
 					<form id="frm-1" class="form-horizontal">
	 					<div class="form-group">
	 						<div class="col-sm-6">
	 							<label>Tên</label>
	 							<input type="text" name="tenGiaNuoc" id="txt-tenGiaNuoc" class="form-control"/>
	 						</div>
	 						<div class="col-sm-6">
	 							<label>Ngày áp dụng</label>
	 							<div class="input-group">
	 								<input type="text" name="ngayApDung" id="txt-ngayApDung" class="form-control chon-ngay input-mask" data-inputmask="'alias': 'dd/mm/yyyy'"/>
	 								<span class="input-group-addon"><i class="fa fa-calendar"></i></span>
	 							</div>
	 						</div>
	 					</div>
	 					<div class="form-group">
	 						<div class="col-sm-6">
		 						<label>Đối tượng</label>
		 						<select name="idDoiTuong" id="cmb-doituong" class="form-control"></select>
	 						</div>
	 						<div class="col-sm-6">
	 							<label>Huyện</label>
	 							<select name="idHuyen" id="cmb-huyen" class="form-control"></select>
	 						</div>
	 					</div>
	 					<div class="form-group">
	 						<div class="col-sm-12">
		 						<label>Phường</label>
		 						<select name="duongDtos" id="cmb-phuong" class="form-control multiselect" multiple="multiple"></select>
	 						</div>
	 					</div>
	 					<div class="form-group">
	 						<div class="col-sm-12">
		 						<div class="checkbox">
			 						<label>
			 							<input type="checkbox" id="chk-phantram" name="phanTram" value="1"/> Phần trăm
			 						</label>
			 					</div>
	 						</div>
	 					</div>
	 					<input type="hidden" name="idGiaNuoc" id="txt-idGiaNuoc"/>
 					</form>
 					<hr/>
 					<div class="row">
 						<div class="col-md-3 co-so">
 							<div class="form-group">
 								<input type="text" id="txt-tu" class="form-control number enter-tab" next="txt-den" placeholder="Từ"/>
 							</div>
 						</div>
 						<div class="col-md-3 co-so">
 							<div class="form-group">
 								<input type="text" id="txt-den" class="form-control number enter-tab" next="txt-dongia" placeholder="Đến"/>
 							</div>
 						</div>
 						<div class="col-md-6 hide phan-tram">
 							<div class="form-group">
 								<input type="text" id="txt-phantram" class="form-control number enter-tab" next="txt-dongia" placeholder="Phần trăm tiêu thụ"/>
 							</div>
 						</div>
 						<div class="col-md-6">
 							<div class="form-group">
 								<div class="input-group">
 									<input type="text" id="txt-dongia" next="btn-add" class="form-control number enter-tab" placeholder="Đơn giá"/>
 									<span class="input-group-btn">
 										<button type="button" class="btn btn-default btn-flat" id="btn-add"><i class="fa fa-check"></i></button>
 										<button type="button" class="btn btn-default btn-flat" id="btn-clr-add"><i class="fa fa-times"></i></button>
 									</span>
 								</div>
 							</div>
 						</div>
 						<input type="hidden" id="txt-stt"/>
 					</div>
 					<div class="jdgrid" id="grid-detail"></div>
 				</div>
 				<div class="box-footer text-center">
 					<button class="btn btn-success btn-flat" type="button" id="btn-ok"><i class="fa fa-check-circle"></i> Lưu</button>&nbsp;&nbsp;
					<button class="btn btn-default btn-flat" type="button" id="btn-cancel"><i class="fa fa-times-circle"></i> Hủy</button>
 				</div>
 			</div>
 		</div>
 	</div>
 </section>
 <!-- /.content -->
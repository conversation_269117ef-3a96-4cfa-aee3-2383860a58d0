<!-- Content Header (Page header) -->
<section class="content-header">
     <h1>
		<th:block th:text="${MODEL.title}"></th:block>
		<a data-toggle="modal" data-target="#modIntro" href="#" title="Hướng dẫn sử dụng"><i class="fa fa-question-circle"></i></a>
		<small></small>
     </h1>
     <ol class="breadcrumb">
         <li>
             <a href=".">
                 <i class="fa fa-home"></i>
                 Trang chủ
             </a>
         </li>
         <li class="active" th:text="${MODEL.title}">Actived page</li>
     </ol>
 </section>
 <!-- Main content -->
 <section class="content">
 	<div class="row">
 		<div class="col-md-7">
 			<div class="box box-primary" id="box-ds">
 				<div class="box-header with-border">
 					<h3 class="box-title"><PERSON><PERSON> quyền thanh toán giao dịch</h3>
 				</div>
 				<div class="box-body">
 					<div class="row">
 						<div class="col-md-6"></div>
 						<div class="col-md-6">
 							<div class="form-group">
 								<div class="input-group">
 									<input type="text" id="txt-keyword" class="form-control" placeholder="Thông tin tìm kiếm"/>
 									<span class="input-group-btn">
 										<button type="button" class="btn btn-default btn-flat" id="btn-search"><i class="fa fa-search"></i></button>
 										<button type="button" class="btn btn-default btn-flat" id="btn-clear-search"><i class="fa fa-times-circle"></i></button>
 									</span>
 								</div>
 							</div>
 						</div>
 					</div>
 					<div class="jdgrid" id="grid-ds"></div>
 				</div>
 				<div class="box-footer">
 					<div class="jdpage" id="page-ds"></div>
 				</div>
 			</div>
 		</div>
 		<div class="col-md-5">
 			<div class="box box-success" id="box-frm">
 				<div class="box-header with-border">
 					<h3 class="box-title">Thông tin thanh toán giao dịch</h3>
 				</div>
 				<div class="box-body">
 					<form id="frm-1" class="form-horizontal">
	 					<div class="form-group">
	 						<div class="col-sm-12">
		 						<label>Nhân viên thanh toán</label>
		 						<select name="idNhanVienGhiThu" id="cmb-nhanvien" class="form-control"></select>
	 						</div>
	 					</div>
	 					<div class="form-group">
	 						<div class="col-sm-12">
		 						<label>Đối tượng</label>
		 						<select name="doiTuongDtos" id="cmb-doituong" class="form-control multiselect" multiple="multiple"></select>
	 						</div>
	 					</div>
	 					<div class="form-group">
	 						<div class="col-sm-12">
		 						<label>Danh sách tổ</label>
		 						<div class="form-control" style="height: 426px;">
									<a href="#" id="chk-all" style="text-decoration:none"><i class="fa fa-check-square-o"></i> Chọn tất cả</a> | 
									<a href="#" id="chk-none" style="text-decoration:none"><i class="fa fa-square-o"></i> Bỏ chọn tất cả</a> |
									<a href="#" id="moRongTatCa" style="text-decoration:none"><i class="fa fa-angle-double-down"></i> Mở rộng tất cả</a> | 
									<a href="#" id="thuGonTatCa" style="text-decoration:none"><i class="fa fa-angle-double-up"></i> Thu gọn tất cả</a>
									<div id="tree" style="overflow-y: scroll; height: 391px;"></div>
				                </div>
	 						</div>
	 					</div>
	 					<input type="hidden" name="idLichGhiThu" id="txt-idlichghithu"/>
 					</form>
 				</div>
 				<div class="box-footer text-center">
 					<button class="btn btn-success btn-flat" type="button" id="btn-ok"><i class="fa fa-check-circle"></i> Lưu</button>&nbsp;&nbsp;
					<button class="btn btn-default btn-flat" type="button" id="btn-cancel"><i class="fa fa-times-circle"></i> Hủy</button>
 				</div>
 			</div>
 		</div>
 	</div>
 </section>
 <!-- /.content -->
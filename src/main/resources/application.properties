# set default profile
#spring.profiles.active: dev
# ===============================
# = DATA SOURCE
# ===============================
spring.datasource.url = ***************************************************************************************
spring.datasource.username = bcsgd
spring.datasource.password = Bcsgd@2019

# ===============================
# = JPA / HIBERNATE
# ===============================
spring.jpa.show-sql = true
spring.jpa.properties.hibernate.dialect = org.hibernate.dialect.MySQL5InnoDBDialect
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
spring.jpa.properties.hibernate.proc.param_null_passing = true
spring.http.multipart.enabled = false
server.servlet.session.timeout = 60m
# ===== Report path
report.path=/var/bcsgd/report/
#report.path=E\:/toolsgd.web/report/

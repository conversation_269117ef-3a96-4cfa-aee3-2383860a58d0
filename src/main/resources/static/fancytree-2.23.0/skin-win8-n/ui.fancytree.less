/*!
 * Fancytree "win8" skin (highlighting the node span instead of title-only).
 *
 * DON'T EDIT THE CSS FILE DIRECTLY, since it is automatically generated from
 * the LESS templates.
 */

// Import common styles
@import "../skin-common.less";


/*******************************************************************************
 * Styles specific to this skin.
 *
 * This section is automatically generated from the `ui-fancytree.less` template.
 ******************************************************************************/

// Borders have NO radius and NO gradients are used!

// both:
//    unselected background: white
//    hover bar (unselected, inactive): #E5F3FB (border: #70C0E7) 'very light blue'
//    active node: #CBE8F6 (border: #26A0DA)  'light blue'
//    active node with hover: wie active node

// Tree view:
//    active node, tree inactive: #F7F7F7 (border: #DEDEDE) 'light gray, selected, but tree not active'

// List view: 
//    selected bar: --> active bar
//    focus  bar: transparent(white) + border 1px solid #3399FF  ()

//    table left/right border: #EDEDED 'light gray'

// Override the variable after the import. 
// NOTE: Variables are always resolved as the last definition, even if it is 
// after where it is used.
@fancy-use-sprites: true;  // false: suppress all background images (i.e. icons)
// Set to `true` to inline icon sprite into CSS:
// @fancy-inline-sprites: true;
@fancy-icon-width: 16px;
@fancy-icon-height: 16px;
@fancy-line-height: 16px;
@fancy-icon-spacing: 3px;

// Use 'data-uri(...)' to embed the image into CSS instead of linking to 'loading.gif':
@fancy-loading-url: data-uri("@{fancy-image-dir}/loading.gif");
// Set to `true` to use `data-uri(...)` which will embed icons.gif into CSS 
// instead of linking to that file:
// @fancy-inline-sprites: true;


/*******************************************************************************
 * Node titles
 */
.fancytree-plain  {
	span.fancytree-node {
		border: @fancy-node-border-width solid transparent;  // avoid jumping, when a border is added on hover
	}
	span.fancytree-node:hover {
		background-color: #E5F3FB;
		border-color: #70C0E7;
	}
	&.fancytree-container.fancytree-treefocus span.fancytree-node.fancytree-focused {
		border-color: #3399FF;
	//	outline: 1px solid #3399FF; 
	}
	span.fancytree-node.fancytree-active,
	span.fancytree-node.fancytree-selected { // active/selcted nodes inside inactive tree
		background-color: #F7F7F7;
		border-color: #DEDEDE;
	}
	&.fancytree-container.fancytree-treefocus span.fancytree-node.fancytree-active,
	&.fancytree-container.fancytree-treefocus span.fancytree-node.fancytree-selected,
	span.fancytree-node.fancytree-active:hover,
	span.fancytree-node.fancytree-selected:hover {
		background-color: #CBE8F6;
		border-color: #26A0DA;
	}
	.fancytree-node.fancytree-selected {
		font-style: italic;
	}
}

/*******************************************************************************
 * 'table' extension
 */
table.fancytree-ext-table tbody {
	tr td {
	  border: 1px solid #EDEDED;
	}
	span.fancytree-node,
	span.fancytree-node:hover { // undo standard tree css
		border: none;
		background: none;
	}
	// Title gets a white background, when hovered. Undo standard node formatting
	// span.fancytree-title:hover {
	//   border: none; //1px solid transparent;
	//   background: inherit;
	//   background: transparent;
	//   background: none;
	//   filter: none;
	// }
	tr:hover  {
		background-color: #E5F3FB;
	    outline: 1px solid #70C0E7;
	}
	// tr:hover td {
	//   outline: 1px solid #D8F0FA;
	// }
	// tr.fancytree-focused {
	// 	border-color: #3399FF;
 //    	outline: 1px dotted black;
	// }
	tr.fancytree-focused span.fancytree-title {
	  outline: 1px dotted black;
	}

	tr.fancytree-active:hover,
	tr.fancytree-selected:hover {
		background-color: #CBE8F6;
	    outline: 1px solid #26A0DA;
	}
	tr.fancytree-active {  // dimmed, if inside inactive tree
		background-color: #F7F7F7;
	    outline: 1px solid #DEDEDE;
	}
	tr.fancytree-selected {  // dimmed, if inside inactive tree
		background-color: #F7F7F7;
	}
}

table.fancytree-ext-table.fancytree-treefocus tbody {
	tr.fancytree-active {
		background-color: #CBE8F6;
	    outline: 1px solid #26A0DA;
	}
	tr.fancytree-selected {
		background-color: #CBE8F6;
	}
}

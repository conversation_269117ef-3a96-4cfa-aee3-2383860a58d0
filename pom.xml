<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>vn.vnpt.camau</groupId>
	<artifactId>toolbc.sgd.web</artifactId>
	<version>1.0.0</version>
	<packaging>jar</packaging>

	<name>toolbc.sgd.web</name>
	<description>He thong bao cao So giao duc</description>

	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>1.5.10.RELEASE</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<java.version>1.8</java.version>
	</properties>

	<repositories>
		<repository>
			<id>jaspersoft-third-party</id>
			<url>http://jaspersoft.jfrog.io/jaspersoft/third-party-ce-artifacts/</url>
		</repository>
	</repositories>

	<dependencies>
		<dependency>
	        <groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>1.16.20</version><!--$NO-MVN-MAN-VER$-->
			<scope>provided</scope>
	    </dependency>
		<dependency>
		    <groupId>net.sf.dozer</groupId>
		    <artifactId>dozer</artifactId>
		    <version>5.5.1</version>
		</dependency>
		<dependency>
            <groupId>org.thymeleaf.extras</groupId>
            <artifactId>thymeleaf-extras-springsecurity4</artifactId>
        </dependency>
		
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-security</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-thymeleaf</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-devtools</artifactId>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
        	<groupId>jasperreports-fonts</groupId>
        	<artifactId>jasperreports-fonts</artifactId>
        	<version>5.6.0</version>
    	</dependency>
    	<dependency>
	    	<groupId>net.sf.jasperreports</groupId>
	    	<artifactId>jasperreports</artifactId>
	    	<version>6.4.1</version>
		</dependency>
		<dependency>
		    <groupId>commons-io</groupId>
		    <artifactId>commons-io</artifactId>
		    <version>2.5</version>
		</dependency>
		<dependency>
		    <groupId>org.apache.poi</groupId>
		    <artifactId>poi-ooxml</artifactId>
		    <version>3.17</version>
		</dependency>
		<dependency>
		    <groupId>commons-fileupload</groupId>
		    <artifactId>commons-fileupload</artifactId>
		    <version>1.3.1</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web-services</artifactId>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<executable>true</executable>
				</configuration>
			</plugin>
		</plugins>
		<!-- <pluginManagement>
			<plugins>
				<plugin>
				    <groupId>org.jvnet.jaxb2.maven2</groupId>
				    <artifactId>maven-jaxb2-plugin</artifactId>
				    <version>0.13.1</version>
				    <executions>
				        <execution>
				            <goals>
				                <goal>generate</goal>
				            </goals>
				        </execution>
				    </executions>
				    <configuration>
				        <schemaLanguage>WSDL</schemaLanguage>
				        <generatePackage>vn.vnpt.camau.capnuoc.qlkh.web.wsdl</generatePackage>
				        <generateDirectory>${project.basedir}/src/main/java</generateDirectory>
				        <schemas>
				            <schema>
				                <url>https://demobenhvienadmin.vnpt-invoice.com.vn/PublishService.asmx?WSDL</url>
				            </schema>
				            <schema>
				            	<url>https://demobenhvienadmin.vnpt-invoice.com.vn/BusinessService.asmx?WSDL</url>
				            </schema>
				            <schema>
				            	<url>https://demobenhvienadmin.vnpt-invoice.com.vn/PortalService.asmx?WSDL</url>
				            </schema>
				        </schemas>
				    </configuration>
				</plugin>
			</plugins>
		</pluginManagement> -->
	</build>

</project>
